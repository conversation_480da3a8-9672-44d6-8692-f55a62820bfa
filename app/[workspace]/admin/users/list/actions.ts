'use server';
import { groups, users, userWorkspace } from '@/app/db/schema';
import { db } from '@/app/db';
import { desc, eq, and, inArray } from 'drizzle-orm';
import { isWorkspaceAdmin } from '@/app/utils/workspace';
import bcrypt from "bcryptjs";

type UserActionParams = {
  email: string;
  password?: string;
  isAdmin: boolean;
  groupId?: string;
};

const handleDatabaseError = (error: unknown, defaultMessage: string) => ({
  success: false,
  message: error instanceof Error ? error.message : defaultMessage
});

const hashPassword = async (password: string) => {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
};

export async function getUserList(workspaceId: string, groupId?: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }
  let result
  try {
    // 首先获取属于该workspace的所有用户ID
    const workspaceUsers = await db.select({
      userId: userWorkspace.userId
    }).from(userWorkspace)
      .where(and(
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      ));

    const userIds = workspaceUsers.map(wu => wu.userId);

    if (userIds.length === 0) {
      return [];
    }

    const baseQuery = {
      orderBy: [desc(users.createdAt)],
      with: {
        group: {
          columns: {
            name: true,
            tokenLimitType: true,
            monthlyTokenLimit: true,
          }
        }
      }
    };

    if (!groupId || groupId === '_all') {
      // 获取所有属于该workspace的用户
      result = await db.query.users.findMany({
        ...baseQuery,
        where: inArray(users.id, userIds)
      });
    } else {
      // 获取指定分组且属于该workspace的用户
      result = await db.query.users.findMany({
        ...baseQuery,
        where: and(
          inArray(users.id, userIds),
          eq(users.groupId, groupId)
        )
      });
    }

    // 获取今天凌晨 0 点的时间戳
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);
    // 处理每条记录的 todayTotalTokens
    result = result.map(user => ({
      ...user,
      todayTotalTokens: new Date(user.usageUpdatedAt) >= today
        ? user.todayTotalTokens
        : 0,
      currentMonthTotalTokens: new Date(user.usageUpdatedAt) >= firstDayOfMonth
        ? user.currentMonthTotalTokens
        : 0,
    }));
    return result;
  } catch (error) {
    throw new Error('Failed to fetch user list');
  }
}

export async function addUser(workspaceId: string, user: UserActionParams & { password: string }) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return handleDatabaseError(null, 'Access denied');
  }
  try {
    const emailExists = await db.query.users.findFirst({
      where: eq(users.email, user.email),
    });
    if (emailExists) return { success: false, message: 'Email has been registered' }
    const hashedPassword = await hashPassword(user.password);

    // 验证分组是否属于当前workspace
    const group = user.groupId ? await db.query.groups.findFirst({
      where: and(eq(groups.id, user.groupId), eq(groups.workspaceId, workspaceId))
    }) : null;
    if (user.groupId && !group) {
      return {
        success: false,
        message: 'Group not found in this workspace'
      }
    }

    // 创建用户
    const [newUser] = await db.insert(users).values({
      email: user.email,
      password: hashedPassword,
      isAdmin: user.isAdmin,
      groupId: user.groupId
    }).returning();

    // 将用户添加到workspace
    await db.insert(userWorkspace).values({
      userId: newUser.id,
      workspaceId: workspaceId,
      role: user.isAdmin ? 'admin' : 'member',
      isActive: true
    });

    return {
      success: true,
      message: 'User added successfully'
    }
  } catch (error) {
    return handleDatabaseError(error, 'User registration failed');
  }
}

export async function deleteUser(workspaceId: string, email: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }
  try {
    // 首先验证用户是否属于当前workspace
    const userInWorkspace = await db.select({
      userId: users.id
    }).from(users)
      .innerJoin(userWorkspace, eq(users.id, userWorkspace.userId))
      .where(and(
        eq(users.email, email),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      ));

    if (userInWorkspace.length === 0) {
      return {
        success: false,
        message: 'User not found in this workspace'
      }
    }

    // 从workspace中移除用户（软删除）
    await db.update(userWorkspace).set({
      isActive: false
    }).where(and(
      eq(userWorkspace.userId, userInWorkspace[0].userId),
      eq(userWorkspace.workspaceId, workspaceId)
    ));

    return {
      success: true,
      message: 'User removed from workspace successfully'
    }
  } catch (error) {
    return {
      success: false,
      message: 'User delete failed'
    }
  }
}

export async function updateUser(workspaceId: string, email: string, user: UserActionParams) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return handleDatabaseError(null, 'Access denied');
  }
  try {
    // 验证用户是否属于当前workspace
    const userInWorkspace = await db.select({
      userId: users.id
    }).from(users)
      .innerJoin(userWorkspace, eq(users.id, userWorkspace.userId))
      .where(and(
        eq(users.email, email),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      ));

    if (userInWorkspace.length === 0) {
      return {
        success: false,
        message: 'User not found in this workspace'
      }
    }

    // 验证分组是否属于当前workspace
    if (user.groupId) {
      const group = await db.query.groups.findFirst({
        where: and(eq(groups.id, user.groupId), eq(groups.workspaceId, workspaceId))
      });
      if (!group) {
        return {
          success: false,
          message: 'Group not found in this workspace'
        }
      }
    }

    const updateData = {
      email: user.email,
      isAdmin: user.isAdmin,
      groupId: user.groupId,
      ...(user.password && {
        password: await hashPassword(user.password)
      })
    }
    await db.update(users).set(updateData).where(eq(users.email, email))

    // 更新workspace中的角色
    await db.update(userWorkspace).set({
      role: user.isAdmin ? 'admin' : 'member'
    }).where(and(
      eq(userWorkspace.userId, userInWorkspace[0].userId),
      eq(userWorkspace.workspaceId, workspaceId)
    ));

    return {
      success: true,
      message: 'User updated successfully'
    }
  } catch (error) {
    return handleDatabaseError(error, 'User update failed');
  }
}