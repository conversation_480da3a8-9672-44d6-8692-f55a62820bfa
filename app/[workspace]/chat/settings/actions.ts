'use server';
import { users, userWorkspace } from '@/app/db/schema';
import { db } from '@/app/db';
import { eq, and } from 'drizzle-orm';
import { auth } from "@/auth";
import bcrypt from "bcryptjs";
import { getWorkspaceInfo } from '@/app/actions/workspace';


export async function updatePassword(email: string, oldPassword: string, newPassword: string,) {
  const session = await auth();
  if (session?.user.email !== email) {
    throw new Error('not allowed');
  }
  try {
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, email),
    });

    if (!existingUser) {
      return {
        success: false,
        message: '该用户不存在',
      };
    }
    const isMatch = await bcrypt.compare(oldPassword, existingUser.password || '');
    if (!isMatch) {
      return {
        success: false,
        message: '旧密码错误',
      };
    }

    let updateResult = null;
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);
    // 更新用户信息
    updateResult = await db.update(users)
      .set({
        password: hashedPassword,
      })
      .where(eq(users.email, email));
    return {
      success: true,
      message: '已更新',
    };

  } catch (error) {
    return {
      success: false,
      message: 'database delete error'
    }
  }
}

export const getUserUsage = async () => {
  const session = await auth();
  if (!session?.user) {
    throw new Error('not allowed');
  }
  let userTodayTotalTokens = 0;
  let userCurrentMonthTotalTokens = 0;
  let userMonthlyTokenLimit = 0;
  let userTokenLimitType = 'limited';
  const userDetail = await db.query.users.findFirst({
    where: eq(users.id, session.user.id),
    with: {
      group: {
        columns: {
          tokenLimitType: true,
          monthlyTokenLimit: true,
        }
      }
    }
  }) as {
    id: string;
    usageUpdatedAt: Date;
    currentMonthTotalTokens: number;
    todayTotalTokens: number;
    group?: {
      tokenLimitType: 'unlimited' | 'limited';
      monthlyTokenLimit: number | null;
    } | null;
  } | null;

  if (userDetail && userDetail.group) {
    const { tokenLimitType, monthlyTokenLimit } = userDetail.group;
    userTokenLimitType = tokenLimitType || 'limited' as const;
    userMonthlyTokenLimit = monthlyTokenLimit || 0;
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const now = new Date();
  const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);

  if (userDetail?.usageUpdatedAt && new Date(userDetail.usageUpdatedAt) < firstDayOfMonth) {
    userTodayTotalTokens = 0;
    userCurrentMonthTotalTokens = 0;
  } else if (userDetail?.usageUpdatedAt && new Date(userDetail.usageUpdatedAt) < today) {
    userTodayTotalTokens = 0;
    userCurrentMonthTotalTokens = userDetail?.currentMonthTotalTokens || 0;
  } else {
    userTodayTotalTokens = userDetail?.todayTotalTokens || 0;
    userCurrentMonthTotalTokens = userDetail?.currentMonthTotalTokens || 0;
  }
  return {
    todayTotalTokens: userTodayTotalTokens,
    currentMonthTotalTokens: userCurrentMonthTotalTokens,
    monthlyTokenLimit: userMonthlyTokenLimit,
    tokenLimitType: userTokenLimitType,
  }
}

/**
 * 获取当前工作空间信息和用户角色
 * @param workspaceId - 工作空间ID
 * @returns 工作空间信息和用户角色
 */
export const getCurrentWorkspaceInfo = async (workspaceId: string) => {
  const session = await auth();
  if (!session?.user.id) {
    return {
      status: 'fail',
      message: 'User not authenticated',
      data: null
    };
  }

  try {
    // 获取工作空间信息
    const workspaceResult = await getWorkspaceInfo(workspaceId);

    if (workspaceResult.status !== 'success' || !workspaceResult.data) {
      return {
        status: 'fail',
        message: 'Workspace not found or access denied',
        data: null
      };
    }

    return {
      status: 'success',
      data: {
        id: workspaceResult.data.id,
        name: workspaceResult.data.name,
        owner: workspaceResult.data.owner,
        plan: workspaceResult.data.plan,
        role: workspaceResult.data.role,
        createdAt: workspaceResult.data.createdAt,
        updatedAt: workspaceResult.data.updatedAt
      }
    };
  } catch (error) {
    console.error('Get current workspace info error:', error);
    return {
      status: 'fail',
      message: 'Failed to get workspace info',
      data: null
    };
  }
}