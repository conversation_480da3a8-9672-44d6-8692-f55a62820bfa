import {
  llmModels,
  llmSettingsTable,
  groupModels,
  groups,
  users,
  workspaces,
  userWorkspace,
  chats,
  messages,
  bots,
  mcpServers,
  mcpTools,
  usageReport,
  searchEngineConfig,
  appSettings
} from './schema';
import { relations } from 'drizzle-orm';

export const modelsGroupsRelations = relations(llmModels, ({ many }) => ({
    groups: many(groupModels),
}))

export const groupsModelsRelations = relations(groups, ({ many }) => ({
    models: many(groupModels),
}))

export const groupsToModelsRelations = relations(groupModels, ({ one }) => ({
    model: one(llmModels, {
        fields: [groupModels.modelId],
        references: [llmModels.id],
    }),
    group: one(groups, {
        fields: [groupModels.groupId],
        references: [groups.id],
    }),
}))

export const providerModelsRelations = relations(llmSettingsTable, ({ many }) => ({
    models: many(llmModels),
}))

export const modelsProvidersRelations = relations(llmModels, ({ one }) => ({
    provider: one(llmSettingsTable, {
        fields: [llmModels.providerId, llmModels.workspaceId],
        references: [llmSettingsTable.provider, llmSettingsTable.workspaceId],
    }),
}))

// user and group relations
export const usersGroupsRelations = relations(groups, ({ many }) => ({
    users: many(users),
}))

export const groupsUsersRelations = relations(users, ({ one }) => ({
    group: one(groups, {
        fields: [users.groupId],
        references: [groups.id],
    }),
}))

// workspace relations
export const workspaceRelations = relations(workspaces, ({ many }) => ({
    userWorkspaces: many(userWorkspace),
    chats: many(chats),
    messages: many(messages),
    bots: many(bots),
    groups: many(groups),
    llmModels: many(llmModels),
    llmSettings: many(llmSettingsTable),
    mcpServers: many(mcpServers),
    usageReports: many(usageReport),
    searchEngineConfigs: many(searchEngineConfig),
    appSettings: many(appSettings),
}))

// user-workspace relations
export const userWorkspaceRelations = relations(userWorkspace, ({ one }) => ({
    user: one(users, {
        fields: [userWorkspace.userId],
        references: [users.id],
    }),
    workspace: one(workspaces, {
        fields: [userWorkspace.workspaceId],
        references: [workspaces.id],
    }),
}))

export const userWorkspaceUsersRelations = relations(users, ({ many }) => ({
    userWorkspaces: many(userWorkspace),
}))

// chat relations
export const chatRelations = relations(chats, ({ one, many }) => ({
    workspace: one(workspaces, {
        fields: [chats.workspaceId],
        references: [workspaces.id],
    }),
    messages: many(messages),
}))

// message relations
export const messageRelations = relations(messages, ({ one }) => ({
    workspace: one(workspaces, {
        fields: [messages.workspaceId],
        references: [workspaces.id],
    }),
    chat: one(chats, {
        fields: [messages.chatId],
        references: [chats.id],
    }),
}))

// bot relations
export const botRelations = relations(bots, ({ one }) => ({
    workspace: one(workspaces, {
        fields: [bots.workspaceId],
        references: [workspaces.id],
    }),
}))

// mcp server relations
export const mcpServerRelations = relations(mcpServers, ({ one, many }) => ({
    workspace: one(workspaces, {
        fields: [mcpServers.workspaceId],
        references: [workspaces.id],
    }),
    tools: many(mcpTools),
}))

export const mcpToolRelations = relations(mcpTools, ({ one }) => ({
    server: one(mcpServers, {
        fields: [mcpTools.serverId],
        references: [mcpServers.id],
    }),
}))

// usage report relations
export const usageReportRelations = relations(usageReport, ({ one }) => ({
    workspace: one(workspaces, {
        fields: [usageReport.workspaceId],
        references: [workspaces.id],
    }),
}))

// search engine config relations
export const searchEngineConfigRelations = relations(searchEngineConfig, ({ one }) => ({
    workspace: one(workspaces, {
        fields: [searchEngineConfig.workspaceId],
        references: [workspaces.id],
    }),
}))

// update existing relations to include workspace
export const groupsWorkspaceRelations = relations(groups, ({ one }) => ({
    workspace: one(workspaces, {
        fields: [groups.workspaceId],
        references: [workspaces.id],
    }),
}))

export const llmModelsWorkspaceRelations = relations(llmModels, ({ one }) => ({
    workspace: one(workspaces, {
        fields: [llmModels.workspaceId],
        references: [workspaces.id],
    }),
}))

export const llmSettingsWorkspaceRelations = relations(llmSettingsTable, ({ one }) => ({
    workspace: one(workspaces, {
        fields: [llmSettingsTable.workspaceId],
        references: [workspaces.id],
    }),
}))

// app settings workspace relations
export const appSettingsWorkspaceRelations = relations(appSettings, ({ one }) => ({
    workspace: one(workspaces, {
        fields: [appSettings.workspaceId],
        references: [workspaces.id],
    }),
}))